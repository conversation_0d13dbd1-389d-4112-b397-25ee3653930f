import torch
import torch.nn as nn
import torch.nn.functional as F
from transformers import UperNetForSemanticSegmentation, AutoImageProcessor

class UPerNetWrapper(nn.Module):
    """
    UPerNet模型的封装类，用于集成到DeepLabV3Plus-Pytorch项目中
    
    UPerNet是一种通用的语义分割框架，可以与不同的视觉骨干网络结合使用，
    如Swin Transformer或ConvNeXt。
    """
    def __init__(self, model_name="openmmlab/upernet-swin-tiny", 
                 num_classes=21, pretrained_backbone=True, output_stride=None):
        """
        初始化UPerNet模型
        
        Args:
            model_name (str): 模型名称，如"openmmlab/upernet-swin-tiny"
            num_classes (int): 分类类别数量
            pretrained_backbone (bool): 是否使用预训练的骨干网络
            output_stride (int): 输出步长，UPerNet不使用此参数，但为了与DeepLabV3+接口兼容而保留
        """
        super(UPerNetWrapper, self).__init__()
        
        self.model_name = model_name
        self.num_classes = num_classes
        
        # 添加图像处理器，用于模型输入预处理
        self.image_processor = AutoImageProcessor.from_pretrained(self.model_name)
        
        # 获取图像处理器的归一化参数
        self.mean = torch.tensor(self.image_processor.image_mean).view(1, 3, 1, 1)
        self.std = torch.tensor(self.image_processor.image_std).view(1, 3, 1, 1)
        
        if pretrained_backbone:
            # 使用预训练模型，并根据类别数量调整输出层
            self.model = UperNetForSemanticSegmentation.from_pretrained(
                self.model_name,
                num_labels=num_classes,
                ignore_mismatched_sizes=True
            )
        else:
            # 从头开始训练，创建配置并初始化模型
            self.model = UperNetForSemanticSegmentation.from_pretrained(
                self.model_name,
                num_labels=num_classes,
                ignore_mismatched_sizes=True
            )
        
        # 为了与DeepLabV3+接口兼容，添加backbone属性
        # 这个属性在main.py中被用于设置BN层的momentum
        self.backbone = self.model.backbone
        
        # 添加classifier属性，用于与DeepLabV3+接口兼容
        # 在main.py中优化器设置时会用到
        self.classifier = nn.ModuleList([
            self.model.decode_head.classifier,
            self.model.auxiliary_head.classifier
        ])
        
        # 记录设备信息，用于归一化参数的设备转换
        self.device = None
    
    def forward(self, x):
        """
        前向传播函数

        Args:
            x (torch.Tensor): 输入图像张量，形状为[B, C, H, W]

        Returns:
            torch.Tensor: 分割结果，形状为[B, num_classes, H, W]
        """
        # 记录输入尺寸，用于后续上采样
        input_shape = x.shape[-2:]
        batch_size = x.shape[0]

        # 记录设备信息
        if self.device is None:
            self.device = x.device
            self.mean = self.mean.to(self.device)
            self.std = self.std.to(self.device)

        # 检查输入是否需要归一化处理
        if x.max() > 1.0:
            # 将像素值从[0,255]缩放到[0,1]
            x = x / 255.0

        # 应用UPerNet预期的归一化
        x_normalized = (x - self.mean) / self.std

        # 处理batch_size=1的情况，避免BatchNorm错误
        if batch_size == 1 and self.training:
            # 临时切换到eval模式进行前向传播，但保持梯度计算
            was_training = self.model.training
            self.model.eval()

            # 不使用no_grad，保持梯度计算
            outputs = self.model(pixel_values=x_normalized)

            # 恢复训练模式
            if was_training:
                self.model.train()
        else:
            # 正常前向传播
            outputs = self.model(pixel_values=x_normalized)

        # 获取logits
        logits = outputs.logits

        # 使用双线性插值将logits上采样到输入图像的尺寸
        upsampled_logits = F.interpolate(
            logits,
            size=input_shape,
            mode="bilinear",
            align_corners=False
        )

        return upsampled_logits

def upernet_swin_tiny(num_classes=21, output_stride=None, pretrained_backbone=True):
    """
    创建基于Swin-Tiny骨干网络的UPerNet模型
    
    Args:
        num_classes (int): 分类类别数量
        output_stride (int): 输出步长，UPerNet不使用此参数，但为了与DeepLabV3+接口兼容而保留
        pretrained_backbone (bool): 是否使用预训练的骨干网络
    
    Returns:
        UPerNetWrapper: 封装的UPerNet模型
    """
    return UPerNetWrapper(
        model_name="openmmlab/upernet-swin-tiny", 
        num_classes=num_classes, 
        pretrained_backbone=pretrained_backbone, 
        output_stride=output_stride
    )

def upernet_swin_small(num_classes=21, output_stride=None, pretrained_backbone=True):
    """
    创建基于Swin-Small骨干网络的UPerNet模型
    
    Args:
        num_classes (int): 分类类别数量
        output_stride (int): 输出步长，UPerNet不使用此参数，但为了与DeepLabV3+接口兼容而保留
        pretrained_backbone (bool): 是否使用预训练的骨干网络
    
    Returns:
        UPerNetWrapper: 封装的UPerNet模型
    """
    return UPerNetWrapper(
        model_name="openmmlab/upernet-swin-small", 
        num_classes=num_classes, 
        pretrained_backbone=pretrained_backbone, 
        output_stride=output_stride
    )

def upernet_swin_base(num_classes=21, output_stride=None, pretrained_backbone=True):
    """
    创建基于Swin-Base骨干网络的UPerNet模型
    
    Args:
        num_classes (int): 分类类别数量
        output_stride (int): 输出步长，UPerNet不使用此参数，但为了与DeepLabV3+接口兼容而保留
        pretrained_backbone (bool): 是否使用预训练的骨干网络
    
    Returns:
        UPerNetWrapper: 封装的UPerNet模型
    """
    return UPerNetWrapper(
        model_name="openmmlab/upernet-swin-base", 
        num_classes=num_classes, 
        pretrained_backbone=pretrained_backbone, 
        output_stride=output_stride
    )

def upernet_convnext_tiny(num_classes=21, output_stride=None, pretrained_backbone=True):
    """
    创建基于ConvNeXt-Tiny骨干网络的UPerNet模型
    
    Args:
        num_classes (int): 分类类别数量
        output_stride (int): 输出步长，UPerNet不使用此参数，但为了与DeepLabV3+接口兼容而保留
        pretrained_backbone (bool): 是否使用预训练的骨干网络
    
    Returns:
        UPerNetWrapper: 封装的UPerNet模型
    """
    return UPerNetWrapper(
        model_name="openmmlab/upernet-convnext-tiny", 
        num_classes=num_classes, 
        pretrained_backbone=pretrained_backbone, 
        output_stride=output_stride
    )
