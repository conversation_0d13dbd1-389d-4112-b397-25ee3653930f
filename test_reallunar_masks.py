#!/usr/bin/env python3
"""
Test script to analyze the existing grayscale mask files in RealLunar dataset
"""

import os
import numpy as np
from PIL import Image
import matplotlib.pyplot as plt

def analyze_mask_file(mask_path):
    """Analyze a single mask file"""
    print(f"\nAnalyzing: {os.path.basename(mask_path)}")
    
    # Load mask as grayscale
    mask_img = Image.open(mask_path).convert('L')
    mask_array = np.array(mask_img, dtype=np.uint8)
    
    print(f"  Image size: {mask_img.size}")
    print(f"  Array shape: {mask_array.shape}")
    print(f"  Data type: {mask_array.dtype}")
    print(f"  Min value: {mask_array.min()}")
    print(f"  Max value: {mask_array.max()}")
    print(f"  Unique values: {np.unique(mask_array)}")
    
    # Count pixels for each unique value
    unique_values, counts = np.unique(mask_array, return_counts=True)
    total_pixels = mask_array.size
    
    print("  Pixel distribution:")
    for val, count in zip(unique_values, counts):
        percentage = (count / total_pixels) * 100
        print(f"    Value {val}: {count} pixels ({percentage:.2f}%)")
    
    return mask_array, unique_values

def main():
    dataset_path = "datasets/data/RealLunar"
    
    print("=== RealLunar Grayscale Mask Analysis ===")
    
    # Find all grayscale mask files
    mask_files = []
    for filename in os.listdir(dataset_path):
        if filename.startswith('g_') and filename.endswith('.png'):
            mask_files.append(os.path.join(dataset_path, filename))
    
    mask_files.sort()
    print(f"Found {len(mask_files)} grayscale mask files")
    
    # Analyze first few files to understand the data
    sample_files = mask_files[:5]  # Analyze first 5 files
    
    all_unique_values = set()
    
    for mask_path in sample_files:
        mask_array, unique_values = analyze_mask_file(mask_path)
        all_unique_values.update(unique_values)
    
    print(f"\n=== Summary ===")
    print(f"All unique values across analyzed files: {sorted(all_unique_values)}")
    
    # Suggest class mapping based on found values
    unique_vals = sorted(all_unique_values)
    print(f"\nSuggested class mapping:")
    class_names = ['Background', 'Small_Rocks', 'Large_Rocks', 'Sky']
    for i, val in enumerate(unique_vals):
        if i < len(class_names):
            print(f"  Pixel value {val} -> Class {i} ({class_names[i]})")
        else:
            print(f"  Pixel value {val} -> Class {i} (Unknown)")
    
    # Visualize one sample
    if sample_files:
        print(f"\nVisualizing sample: {os.path.basename(sample_files[0])}")
        mask_img = Image.open(sample_files[0]).convert('L')
        mask_array = np.array(mask_img)
        
        plt.figure(figsize=(10, 5))
        
        plt.subplot(1, 2, 1)
        plt.imshow(mask_array, cmap='gray')
        plt.title('Grayscale Mask')
        plt.colorbar()
        
        plt.subplot(1, 2, 2)
        plt.imshow(mask_array, cmap='viridis')
        plt.title('Mask with Color Map')
        plt.colorbar()
        
        plt.tight_layout()
        plt.savefig('reallunar_mask_sample.png', dpi=150, bbox_inches='tight')
        print("Sample visualization saved as 'reallunar_mask_sample.png'")

if __name__ == '__main__':
    main()
