#!/usr/bin/env python3
"""
Test the modified RealLunar dataset with new merge_rocks logic
"""

import sys
import os
sys.path.append('.')

from datasets.reallunar import RealLunarDataset
import numpy as np

def test_merge_logic():
    dataset_root = 'datasets/data'
    
    print("=== Testing RealLunar Dataset with New Merge Logic ===")
    
    # Test both modes
    for merge_rocks in [False, True]:
        mode_name = "2-class (merged)" if merge_rocks else "4-class (full)"
        print(f"\n--- Testing {mode_name} mode ---")
        
        try:
            dataset = RealLunarDataset(root=dataset_root, split='test', merge_rocks=merge_rocks)
            print(f"Dataset loaded successfully!")
            print(f"  Samples: {len(dataset)}")
            print(f"  Classes: {dataset.num_classes}")
            print(f"  Class names: {dataset.get_class_names()}")
            
            if len(dataset) > 0:
                # Test loading first sample
                image, mask = dataset[0]
                mask_array = np.array(mask)
                unique_classes, counts = np.unique(mask_array, return_counts=True)
                total_pixels = mask_array.size
                
                print(f"  Sample 0:")
                print(f"    Image shape: {np.array(image).shape}")
                print(f"    Mask shape: {mask_array.shape}")
                print(f"    Mask unique values: {unique_classes}")
                print(f"    Class distribution:")
                
                for cls, count in zip(unique_classes, counts):
                    percentage = (count / total_pixels) * 100
                    class_name = dataset.class_names[cls] if cls < len(dataset.class_names) else f"Unknown_{cls}"
                    print(f"      Class {cls} ({class_name}): {count} pixels ({percentage:.2f}%)")
                
        except Exception as e:
            print(f"Error loading dataset: {e}")
            import traceback
            traceback.print_exc()

if __name__ == '__main__':
    test_merge_logic()
