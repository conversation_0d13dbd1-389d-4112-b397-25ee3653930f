import os
import numpy as np
from PIL import Image
import torch
from torch.utils import data
from datasets import utils


class RealLunarDataset(data.Dataset):
    """RealLunar Dataset for semantic segmentation

    A real lunar surface dataset containing terrain segmentation data.
    Dataset contains PCAM and TCAM images with grayscale mask annotations.

    Classes:
        0: Background (lunar surface, black pixels)
        1: Small rocks (small rocks/features, pixel value 1)
        2: Large rocks (large rocks/features, pixel value 2)
        3: Sky (sky regions, pixel value 3)
    """

    def __init__(self, root, split='train', transform=None, camera_type='both', merge_rocks=False, use_full_dataset=False):
        """
        Args:
            root (str): Root directory of the dataset
            split (str): Dataset split ('train', 'val', 'test')
            transform: Data augmentation transforms
            camera_type (str): Camera type to use ('PCAM', 'TCAM', 'both')
            merge_rocks (bool): If True, merge small and large rocks into single 'Rocks' class
                               If False, keep separate small rocks, large rocks, and sky classes
            use_full_dataset (bool): If True, use all samples regardless of split
        """
        self.root = os.path.expanduser(root)
        self.split = split
        self.transform = transform
        self.camera_type = camera_type
        self.merge_rocks = merge_rocks
        self.use_full_dataset = use_full_dataset

        # Dataset path
        self.dataset_path = os.path.join(self.root, 'RealLunar')

        # Class information - depends on merge_rocks setting
        if merge_rocks:
            self.num_classes = 2  # Background+Sky (merged) + Rocks (merged)
            self.class_names = ['Background', 'Rocks']
            print("RealLunar Dataset Mode: 2-class (Background + Rocks) - Merged mode")
        else:
            self.num_classes = 4  # Background + Small rocks + Large rocks + Sky
            self.class_names = ['Background', 'Small_Rocks', 'Large_Rocks', 'Sky']
            print("RealLunar Dataset Mode: 4-class (Background + Small_Rocks + Large_Rocks + Sky) - Full mode")

        self.ignore_index = 255

        # Color mapping for visualization
        if merge_rocks:
            self.colors = [
                [0, 0, 0],      # Background (merged with sky) - Black
                [0, 0, 255],    # Rocks (merged) - Blue
            ]
        else:
            self.colors = [
                [0, 0, 0],      # Background - Black
                [0, 255, 0],    # Small rocks - Green
                [0, 0, 255],    # Large rocks - Blue
                [255, 0, 0],    # Sky - Red
            ]

        # Get all available samples
        self.samples = self._get_samples()
        
        # Split dataset: 70% train, 20% val, 10% test
        self.train_samples, self.val_samples, self.test_samples = self._split_dataset()

        # Select samples based on split or use full dataset
        if self.use_full_dataset:
            self.current_samples = self.samples
            print(f"RealLunar Dataset - full dataset: {len(self.current_samples)} samples")
        else:
            if split == 'train':
                self.current_samples = self.train_samples
            elif split == 'val':
                self.current_samples = self.val_samples
            elif split == 'test':
                self.current_samples = self.test_samples
            else:
                raise ValueError(f"Invalid split: {split}. Must be 'train', 'val', or 'test'")

            print(f"RealLunar Dataset - {split}: {len(self.current_samples)} samples")

    def _get_samples(self):
        """Get all available samples from PCAM and TCAM data with mask files"""
        samples = []

        # Scan for image files and their corresponding mask files
        for camera in ['PCAM', 'TCAM']:
            if self.camera_type not in [camera, 'both']:
                continue

            # Find all image files for this camera type
            for filename in os.listdir(self.dataset_path):
                if filename.startswith(camera) and filename.endswith('.png') and '_mask' not in filename:
                    # Extract frame ID from filename (e.g., PCAM1.png -> 1)
                    try:
                        frame_id = int(filename.replace(camera, '').replace('.png', ''))
                    except ValueError:
                        continue

                    img_path = os.path.join(self.dataset_path, filename)
                    mask_filename = f"g_{camera}{frame_id}.png"
                    mask_path = os.path.join(self.dataset_path, mask_filename)

                    # Check if both image and mask exist
                    if os.path.exists(img_path) and os.path.exists(mask_path):
                        samples.append({
                            'camera': camera,
                            'frame_id': frame_id,
                            'image': img_path,
                            'mask': mask_path
                        })
                    else:
                        print(f"Warning: Missing mask file for {filename}, expected {mask_filename}")

        # Sort by camera type and frame id
        samples.sort(key=lambda x: (x['camera'], x['frame_id']))
        print(f"Found {len(samples)} image-mask pairs")
        return samples
    
    def _split_dataset(self):
        """Split dataset into train/val/test with 7:2:1 ratio"""
        total_samples = len(self.samples)
        
        # Calculate split indices
        train_end = int(0.7 * total_samples)
        val_end = int(0.9 * total_samples)
        
        train_samples = self.samples[:train_end]
        val_samples = self.samples[train_end:val_end]
        test_samples = self.samples[val_end:]
        
        print(f"Dataset split - Train: {len(train_samples)}, Val: {len(val_samples)}, Test: {len(test_samples)}")

        return train_samples, val_samples, test_samples

    def _load_mask(self, mask_path, target_size=None):
        """Load mask from grayscale image file and process according to merge_rocks setting

        Args:
            mask_path: Path to the mask image file
            target_size: Target size (width, height) to resize mask to match image

        Returns:
            numpy array with class indices
        """
        # Load mask as grayscale
        mask_img = Image.open(mask_path).convert('L')

        # Resize mask to match target size if provided
        if target_size is not None:
            mask_img = mask_img.resize(target_size, Image.NEAREST)

        mask_array = np.array(mask_img, dtype=np.uint8)

        # Map original pixel values to class indices
        # Based on user feedback: main values are [0, 29, 76, 150]
        # Corrected mapping: 0->Background, 29->Large_Rocks, 76->Sky, 150->Small_Rocks
        class_mask = np.zeros_like(mask_array)

        # Background (black pixels)
        class_mask[mask_array == 0] = 0

        # Small rocks (pixel value ~150)
        class_mask[mask_array == 150] = 1

        # Large rocks (pixel value ~29)
        class_mask[mask_array == 29] = 2

        # Sky (pixel value ~76)
        class_mask[mask_array == 76] = 3

        # Handle other pixel values by mapping them to closest class
        other_values = ~np.isin(mask_array, [0, 29, 76, 150])
        if np.any(other_values):
            # Map other values to closest main value
            # Reference values: [0->Background, 150->Small_Rocks, 29->Large_Rocks, 76->Sky]
            reference_values = [0, 150, 29, 76]
            reference_classes = [0, 1, 2, 3]

            for pixel_val in np.unique(mask_array[other_values]):
                distances = [abs(pixel_val - v) for v in reference_values]
                closest_idx = distances.index(min(distances))
                closest_class = reference_classes[closest_idx]
                class_mask[mask_array == pixel_val] = closest_class

        if self.merge_rocks:
            # Merge rocks (classes 1&2) and merge background&sky (classes 0&3)
            # Original: 0=Background, 1=Small_Rocks, 2=Large_Rocks, 3=Sky
            # Merged:   0=Background+Sky(merged), 1=Rocks(merged)
            merged_mask = np.zeros_like(class_mask)
            merged_mask[(class_mask == 0) | (class_mask == 3)] = 0  # Merge background and sky to 0
            merged_mask[(class_mask == 1) | (class_mask == 2)] = 1  # Merge rocks to 1
            return merged_mask
        else:
            # Keep original 4-class system
            return class_mask


    
    def __getitem__(self, index):
        """Get a sample from the dataset"""
        sample = self.current_samples[index]

        # Load image and convert to RGB
        image = Image.open(sample['image']).convert('RGB')

        # Load mask from file and resize to match image size
        mask_array = self._load_mask(sample['mask'], target_size=image.size)
        mask = Image.fromarray(mask_array)

        # Apply transforms
        if self.transform is not None:
            image, mask = self.transform(image, mask)

        return image, mask
    
    def __len__(self):
        return len(self.current_samples)
    
    def get_class_names(self):
        """Get class names"""
        return self.class_names
    
    def get_class_colors(self):
        """Get class colors for visualization (RGB format)"""
        return self.colors

    def decode_target(self, target):
        """Convert class indices to RGB colors for visualization

        Args:
            target: numpy array with class indices

        Returns:
            numpy array with RGB colors for visualization
        """
        # Define color mapping based on mode
        color_map = np.array(self.colors, dtype=np.uint8)

        # Handle invalid class indices
        target = np.clip(target, 0, len(color_map) - 1)

        # Convert to RGB
        rgb_target = color_map[target]

        return rgb_target


if __name__ == '__main__':
    # Test the dataset
    import matplotlib.pyplot as plt

    # Test dataset loading
    dataset_root = '../datasets/data'

    print("Testing RealLunar Dataset in both modes...")

    for merge_rocks in [False, True]:
        mode_name = "3-class (merged rocks)" if merge_rocks else "4-class (full)"
        print(f"\n=== Testing {mode_name} mode ===")

        for split in ['train', 'val', 'test']:
            try:
                dataset = RealLunarDataset(root=dataset_root, split=split, merge_rocks=merge_rocks)
                print(f"\n{split.upper()} Dataset ({mode_name}):")
                print(f"  Samples: {len(dataset)}")
                print(f"  Classes: {dataset.num_classes}")
                print(f"  Class names: {dataset.get_class_names()}")

                if len(dataset) > 0:
                    # Test loading first sample
                    image, mask = dataset[0]
                    print(f"  Sample 0 - Image shape: {np.array(image).shape}, Mask shape: {np.array(mask).shape}")
                    print(f"  Mask unique values: {np.unique(np.array(mask))}")

            except Exception as e:
                print(f"Error loading {split} dataset: {e}")
