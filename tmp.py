import argparse
import os
import time
import subprocess
import GPUtil
import psutil

# Setting up argparse to accept command line arguments
parser = argparse.ArgumentParser(description='Monitor GPU and restart training script if GPU utilization falls below threshold for 10 consecutive checks.')
#parser.add_argument('training_command', type=str, help='The command used to run the training script, including all necessary arguments.')
parser.add_argument('--gpu_threshold', type=int, default=5, help='GPU usage threshold to restart the script (default: 0)')
parser.add_argument('--check_interval', type=int, default=100, help='How often to check GPU usage, in seconds (default: 60)')
args = parser.parse_args()
first_time_launch = True
start_time = time.time()

def is_same_string_ignore_whitespace(str1, str2):
    """ Check if two strings are the same, ignoring whitespace """
    return "".join(str1.split()) == "".join(str2.split())

def is_gpu_in_use(threshold):
    """ Check if the GPU is being used above the given threshold """
    GPUs = GPUtil.getGPUs()
    for gpu in GPUs:
        if gpu.load * 100 > threshold:
            return True
    return False

def restart_training_process():
    """ Restart the training process """
    # Killing existing training processes, if any
    while True:
        find_same_command = False
        for proc in psutil.process_iter(['pid', 'name', 'cmdline']):
            # if args.training_command == " ".join(proc.info['cmdline']):
            if is_same_string_ignore_whitespace(args.training_command, " ".join(proc.info['cmdline'])):
                print("Found existing training process. Killing it.", " ".join(proc.info['cmdline']))
                find_same_command = True
                proc.kill()
                time.sleep(5)
        if not find_same_command:
            break

    global first_time_launch
    first_time_launch = False
    # Starting a new training process
    print("Launching new training process.", args.training_command)
    subprocess.Popen(args.training_command, shell=True)

def main():
    idle_count = 0  # Counter for consecutive idle checks
    first_time_launch = True
    while True:
        if not is_gpu_in_use(args.gpu_threshold):
            idle_count += 1
            if not first_time_launch and (time.time() - start_time) < 3600 and idle_count >= 10:
                print("Error happen for training")
                exit(0)
            print(f"GPU utilization below threshold. Idle count: {idle_count}")
            if idle_count >= 10 or first_time_launch:
                print("Detected 10 consecutive idle intervals. Restarting training process.")
                restart_training_process()
                first_time_launch = False
                idle_count = 0
        else:
            idle_count = 0  # Reset the counter if GPU is in use

        time.sleep(args.check_interval)

if __name__ == "__main__":
    main()