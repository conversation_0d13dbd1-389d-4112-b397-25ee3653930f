import os
import numpy as np
from PIL import Image

dataset_path = 'datasets/data/RealLunar'

# 检查几个掩码文件的像素值分布
mask_files = ['g_PCAM1.png', 'g_PCAM2.png', 'g_TCAM1.png', 'g_TCAM2.png']

print('Current pixel value distribution in mask files:')
print('=' * 60)

all_values = []
for mask_file in mask_files:
    mask_path = os.path.join(dataset_path, mask_file)
    if os.path.exists(mask_path):
        mask_img = Image.open(mask_path).convert('L')
        mask_array = np.array(mask_img, dtype=np.uint8)
        
        unique_values, counts = np.unique(mask_array, return_counts=True)
        total_pixels = mask_array.size
        
        print(f'\n{mask_file}:')
        for val, count in zip(unique_values, counts):
            percentage = (count / total_pixels) * 100
            print(f'  Pixel value {val}: {count} pixels ({percentage:.2f}%)')
            all_values.extend([val] * count)

print(f'\n' + '=' * 60)
print('Overall distribution across all checked files:')
all_values = np.array(all_values)
unique_values, counts = np.unique(all_values, return_counts=True)
total_pixels = len(all_values)

for val, count in zip(unique_values, counts):
    percentage = (count / total_pixels) * 100
    print(f'  Pixel value {val}: {count} pixels ({percentage:.2f}%)')

print(f'\nCurrent mapping in code:')
print(f'  Pixel value 0 -> Class 0 (Background)')
print(f'  Pixel value 150 -> Class 1 (Small_Rocks)')
print(f'  Pixel value 29 -> Class 2 (Large_Rocks)')
print(f'  Pixel value 76 -> Class 3 (Sky)')

# 测试当前数据集的映射
print(f'\n' + '=' * 60)
print('Testing current dataset mapping:')

import sys
sys.path.append('.')
from datasets.reallunar import RealLunarDataset

try:
    dataset = RealLunarDataset(root='datasets/data', split='test', merge_rocks=False)
    if len(dataset) > 0:
        image, mask = dataset[0]
        mask_array = np.array(mask)
        unique_classes, counts = np.unique(mask_array, return_counts=True)
        total_pixels = mask_array.size
        
        print(f'First sample after dataset processing:')
        for cls, count in zip(unique_classes, counts):
            percentage = (count / total_pixels) * 100
            class_name = dataset.class_names[cls] if cls < len(dataset.class_names) else f"Unknown_{cls}"
            print(f'  Class {cls} ({class_name}): {count} pixels ({percentage:.2f}%)')
            
except Exception as e:
    print(f'Error testing dataset: {e}')
