import os
import numpy as np
from PIL import Image

dataset_path = 'datasets/data/RealLunar'
files_to_check = ['g_PCAM1.png', 'g_PCAM2.png', 'g_TCAM1.png', 'g_TCAM2.png']

all_unique_values = set()

for filename in files_to_check:
    mask_file = os.path.join(dataset_path, filename)
    if os.path.exists(mask_file):
        print(f'\n=== {filename} ===')
        mask_img = Image.open(mask_file).convert('L')
        mask_array = np.array(mask_img, dtype=np.uint8)
        
        unique_values = np.unique(mask_array)
        all_unique_values.update(unique_values)
        print(f'Unique values: {unique_values}')
        
        unique_values, counts = np.unique(mask_array, return_counts=True)
        total_pixels = mask_array.size
        
        for val, count in zip(unique_values, counts):
            percentage = (count / total_pixels) * 100
            print(f'  Value {val}: {percentage:.2f}%')

print(f'\n=== Summary ===')
print(f'All unique values found: {sorted(all_unique_values)}')
