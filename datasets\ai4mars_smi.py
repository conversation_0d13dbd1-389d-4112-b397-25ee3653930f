import json
import os
from collections import namedtuple

import torch
import torch.utils.data as data
from PIL import Image
import numpy as np


class AI4MarsSMI(data.Dataset):
    """AI4Mars-SMI Dataset.
    
    **Parameters:**
        - **root** (string): Root directory of dataset where directory 'leftImg8bit' and 'gtFine' or 'gtCoarse' are located.
        - **split** (string, optional): The image split to use, 'train', 'test' or 'val' if mode="gtFine" otherwise 'train', 'train_extra' or 'val'
        - **mode** (string, optional): The quality mode to use, 'gtFine' or 'gtCoarse' or 'color'. Can also be a list to output a tuple with all specified target types.
        - **transform** (callable, optional): A function/transform that takes in a PIL image and returns a transformed version. E.g, ``transforms.RandomCrop``
        - **target_transform** (callable, optional): A function/transform that takes in the target and transforms it.
    """

    # Based on https://github.com/mcordts/cityscapesScripts
    AI4MarsSMIClass = namedtuple('AI4MarsSMIClass', ['name', 'id', 'train_id', 'category', 'category_id',
                                                     'has_instances', 'ignore_in_eval', 'color'])
    classes = [
                ### soil 0 green #########################
        AI4MarsSMIClass('Soil',            0, 0, 'Soil', 0, False, False, (0x56, 0x94, 0x1E)),# (0x56, 0x94, 0x1E)
                ### bedrock 1 yellow ####################
        AI4MarsSMIClass('Bedrock',          1, 1, 'Bedrock', 0, False, False, (0xA0, 0x20, 0xF0)),
                ### sand 2 purple #######################
        AI4MarsSMIClass('Sand', 2, 2, 'Sand', 2, False, False, (0xCC, 0xFF, 0x42)),
                ### big rock 3 red ######################
        AI4MarsSMIClass('Big Rock',           3, 3, 'Big Rock', 0, False, False, (0xFF, 0x00, 0x00)),
                ### robot & distance > 30m 255 blue #####
        AI4MarsSMIClass('BackgroundAndRover',               -1, 255 , 'void', 0, False, False, (0x42, 0xCC, 0xFF)),
    ]

    train_id_to_color = [c.color for c in classes if (c.train_id != -1 and c.train_id != 255)]
    train_id_to_color.append([0, 0, 0])
    train_id_to_color = np.array(train_id_to_color)
    id_to_train_id = np.array([c.train_id for c in classes])
    
    def __init__(self, root, split='train', mode='--', target_type='semantic', transform=None):
        self.root = os.path.expanduser(root)
        self.mode = '--'
        self.target_type = target_type
        
        # 特殊处理testM1、testM2、testM3
        if split in ['testM1', 'testM2', 'testM3']:
            self.images_dir = os.path.join(self.root, 'AI4Mars-SMI', 'images', 'test')
            self.targets_dir = os.path.join(self.root, 'AI4Mars-SMI', 'annotations', split)
        elif split == 'test':
            # 如果是test分割，但annotations中没有test目录，则使用val目录
            self.images_dir = os.path.join(self.root, 'AI4Mars-SMI', 'images', split)
            test_dir = os.path.join(self.root, 'AI4Mars-SMI', 'annotations', split)
            if not os.path.isdir(test_dir):
                self.targets_dir = os.path.join(self.root, 'AI4Mars-SMI', 'annotations', 'val')
            else:
                self.targets_dir = test_dir
        else:
            self.images_dir = os.path.join(self.root, 'AI4Mars-SMI', 'images', split)
            self.targets_dir = os.path.join(self.root, 'AI4Mars-SMI', 'annotations', split)
            
        self.transform = transform

        self.split = split
        self.images = []
        self.targets = []

        if split not in ['train', 'val', 'test', 'testM1', 'testM2', 'testM3']:
            raise ValueError('Invalid split for mode! Please use split="train", split="test", split="val"'
                             ' or one of ["testM1", "testM2", "testM3"]')
        
        if not os.path.isdir(self.images_dir) or not os.path.isdir(self.targets_dir):
            raise RuntimeError('Dataset not found or incomplete. Please make sure all required folders for the'
                              ' specified "split" and "mode" are inside the "root" directory')
        
        for file_name in os.listdir(self.images_dir):
            self.images.append(os.path.join(self.images_dir, file_name))
            
            targetname, file_extension = os.path.splitext(file_name)
            targetname= targetname + ".png"
            self.targets.append(os.path.join(self.targets_dir, targetname))

                
    @classmethod
    def encode_target(cls, target):
        return cls.id_to_train_id[np.array(target)]

    @classmethod
    def decode_target(cls, target):
        target[target == 255] = 4
        return cls.train_id_to_color[target]

    def __getitem__(self, index):
        """
        Args:
            index (int): Index
        Returns:
            tuple: (image, target) where target is a tuple of all target types if target_type is a list with more
            than one item. Otherwise target is a json object if target_type="polygon", else the image segmentation.
        """
        image = Image.open(self.images[index]).convert('RGB')
        target = Image.open(self.targets[index])
        
        if self.transform:
            image, target = self.transform(image, target)
            
        target = self.encode_target(target)
        
        return image, target

    def __len__(self):
        return len(self.images)
    
    def __repr__(self):
        fmt_str = 'Dataset ' + self.__class__.__name__ + '\n'
        fmt_str += '    Number of datapoints: {}\n'.format(self.__len__())
        fmt_str += '    Split: {}\n'.format(self.split)
        fmt_str += '    Mode: {}\n'.format(self.mode)
        fmt_str += '    Type: {}\n'.format(self.target_type)
        fmt_str += '    Root Location: {}\n'.format(self.root)
        fmt_str += '    Transforms (if any): {}\n'.format(self.transform.__repr__().replace('\n', '\n' + ' ' * 13))
        return fmt_str
