#!/usr/bin/env python3
"""
Test the fixed RealLunar dataset with size matching
"""

import sys
import os
sys.path.append('.')

from datasets.reallunar import RealLunarDataset
import numpy as np
from PIL import Image

def test_size_matching():
    dataset_root = 'datasets/data'
    
    print("=== Testing RealLunar Dataset Size Matching ===")
    
    try:
        # Test with 'test' split to get all data
        dataset = RealLunarDataset(root=dataset_root, split='test', merge_rocks=False)
        print(f"Dataset loaded successfully!")
        print(f"  Samples: {len(dataset)}")
        print(f"  Classes: {dataset.num_classes}")
        
        if len(dataset) > 0:
            # Test loading first few samples
            for i in range(min(3, len(dataset))):
                print(f"\n--- Testing sample {i} ---")
                sample_info = dataset.current_samples[i]
                print(f"Image file: {os.path.basename(sample_info['image'])}")
                print(f"Mask file: {os.path.basename(sample_info['mask'])}")
                
                # Load original files to check sizes
                orig_img = Image.open(sample_info['image'])
                orig_mask = Image.open(sample_info['mask'])
                print(f"Original image size: {orig_img.size}")
                print(f"Original mask size: {orig_mask.size}")
                
                # Load through dataset
                image, mask = dataset[i]
                print(f"Dataset image shape: {np.array(image).shape}")
                print(f"Dataset mask shape: {np.array(mask).shape}")
                print(f"Mask unique values: {np.unique(np.array(mask))}")
                
                # Check if sizes match
                img_array = np.array(image)
                mask_array = np.array(mask)
                if img_array.shape[:2] == mask_array.shape[:2]:
                    print("✅ Image and mask sizes match!")
                else:
                    print("❌ Size mismatch!")
                    
        print("\n=== Test completed ===")
        
    except Exception as e:
        print(f"Error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == '__main__':
    test_size_matching()
