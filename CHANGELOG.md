# Changelog

## [2.0.0] - 2025-06-10

### Added
- Added support for SegFormer models with 6 variants (B0-B5) with different model sizes
- Added support for Mask2Former models with 7 variants using different backbones and pre-training datasets
- Added support for UPerNet models with 4 variants using Swin Transformer and ConvNeXt backbones
- Integrated 17 new state-of-the-art semantic segmentation models, increasing total supported models from 12 to 29
- Added `transformers` library dependency for Hugging Face model support
- Created new model implementation files: `network/segformer.py`, `network/mask2former.py`, `network/upernet.py`
- Added 17 new model factory functions to `network/modeling.py`
- Added adaptive optimizer selection: AdamW for Transformer models, SGD for CNN models
- Added smart learning rate adjustment (0.1x) for Transformer models
- Added comprehensive integration summary documentation (`INTEGRATION_SUMMARY.md`)
- Created model testing scripts (`test_models.py`, `test_fixed_models.py`)

### Changed
- Enhanced `main.py` with adaptive training strategies for different model architectures
- Modified optimizer configuration to automatically select appropriate optimizer based on model type
- Updated BN momentum setting logic to safely handle models with different backbone structures
- Improved error handling for models without traditional backbone structure

### Fixed
- Fixed BatchNorm errors when training with batch_size=1 for Transformer models
- Resolved gradient computation issues in Transformer models during training
- Fixed pre-trained weight loading for models with different number of classes by adding `ignore_mismatched_sizes=True`
- Fixed gradient flow issues by removing `torch.no_grad()` while maintaining BatchNorm compatibility

### Performance Notes
- Transformer models require more GPU memory than CNN models
- Recommended batch_size: 2-4 for Transformer models, 4-8 for CNN models
- Training time may vary between model architectures

### Backward Compatibility
- All existing DeepLabV3+ functionality preserved
- Existing training scripts work without modification
- Same command-line interface maintained for all models

---

## [1.0.3] - 2025-06-09

### Added
- Added support for the AI4Mars-SMI dataset, which is similar to AI4Mars but with additional training images
- Created a new dataset class `AI4MarsSMI` in `datasets/ai4mars_smi.py`
- Updated the command line interface to accept 'AI4Mars-SMI' as a valid dataset option
- Added training and evaluation commands for AI4Mars-SMI to MODEL_COMMANDS.md

### Changed
- Updated the dataset loading logic in `main.py` to handle the AI4Mars-SMI dataset
- Modified `datasets/__init__.py` to import the new AI4MarsSMI class

## [1.0.2] - 2025-06-09

### Fixed
- Fixed path handling issues to ensure correct path processing across different operating systems (Windows and Linux)
- Added special handling logic for the `test` split: when the `annotations` directory doesn't have a `test` subdirectory, it automatically falls back to using the `val` directory
- Changed the default value of the `test_only` parameter from `True` to `False` so the program runs in training mode by default
- Fixed checkpoint loading logic by adding empty string checks to ensure the model doesn't attempt to load when the checkpoint path is empty

## [1.0.1] - 2025-06-09

### Added
- Added support for multiple test sets in AI4Mars dataset (testM1, testM2, testM3)
- Added new command line argument `--test_split` to specify which test set to use
- Test split options: 'test', 'testM1', 'testM2', 'testM3'

### Modified
- Updated `ai4mars.py` to handle different test split directories
- Modified dataset loading logic to map 'testM1', 'testM2', 'testM3' splits to the correct directories
- Updated `get_dataset()` function in `main.py` to use the specified test split when in test mode

### Fixed
- Fixed issue where the code couldn't find test annotations when they were split into multiple directories
- Maintained compatibility with the original directory structure

## Usage
To test a model with a specific test split:
```
python main.py --model deeplabv3plus_resnet101 --dataset AI4Mars --crop_size 512 --batch_size 4 --ckpt path/to/model.pth --test_only --save_val_results --test_split testM1
```

Available test splits:
- `test`: Uses the default test set (if available)
- `testM1`: Uses the testM1 test set
- `testM2`: Uses the testM2 test set
- `testM3`: Uses the testM3 test set

## Directory Structure
The code now expects the following directory structure for AI4Mars dataset:
```
datasets/data/AI4Mars/
├── annotations/
│   ├── testM1/     (Test annotations set 1)
│   ├── testM2/     (Test annotations set 2)
│   ├── testM3/     (Test annotations set 3)
│   ├── train/      (Training annotations)
│   └── val/        (Validation annotations)
└── images/
    ├── test/       (Test images for all test sets)
    ├── train/      (Training images)
    └── val/        (Validation images)
```
