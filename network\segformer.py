import torch
import torch.nn as nn
import torch.nn.functional as F
from transformers import SegformerForSemanticSegmentation, SegformerConfig

class SegFormerWrapper(nn.Module):
    """
    SegFormer模型的封装类，用于集成到DeepLabV3Plus-Pytorch项目中
    """
    def __init__(self, model_name="nvidia/mit-b0", num_classes=21, pretrained_backbone=True, output_stride=None):
        """
        初始化SegFormer模型
        
        Args:
            model_name (str): 模型名称，如"nvidia/mit-b0"、"nvidia/mit-b1"等
            num_classes (int): 分类类别数量
            pretrained_backbone (bool): 是否使用预训练的骨干网络
            output_stride (int): 输出步长，SegFormer不使用此参数，但为了与DeepLabV3+接口兼容而保留
        """
        super(SegFormerWrapper, self).__init__()
        
        self.model_name = model_name
        
        if pretrained_backbone:
            # 使用预训练模型，并根据类别数量调整输出层
            self.model = SegformerForSemanticSegmentation.from_pretrained(
                self.model_name,
                num_labels=num_classes,
                ignore_mismatched_sizes=True,
                reshape_last_stage=True
            )
        else:
            # 从头开始训练，创建配置并初始化模型
            config = SegformerConfig.from_pretrained(self.model_name)
            config.num_labels = num_classes
            self.model = SegformerForSemanticSegmentation(config)
    
    def forward(self, x):
        """
        前向传播函数
        
        Args:
            x (torch.Tensor): 输入图像张量，形状为[B, C, H, W]
        
        Returns:
            torch.Tensor: 分割结果，形状为[B, num_classes, H, W]
        """
        # 记录输入尺寸，用于后续上采样
        input_shape = x.shape[-2:]
        
        # SegFormer期望输入形状为[B, C, H, W]，与DeepLabV3+相同
        # 但SegFormer返回的是一个包含logits的字典，需要提取logits
        outputs = self.model(pixel_values=x)
        
        # 获取logits并上采样到原始输入尺寸
        logits = outputs.logits
        
        # 使用双线性插值将logits上采样到输入图像的尺寸
        upsampled_logits = F.interpolate(
            logits,
            size=input_shape,
            mode="bilinear",
            align_corners=False
        )
        
        # 返回上采样后的logits，使其与DeepLabV3+的输出格式兼容
        return upsampled_logits

def segformer_b0(num_classes=21, output_stride=None, pretrained_backbone=True):
    """
    创建SegFormer-B0模型
    
    Args:
        num_classes (int): 分类类别数量
        output_stride (int): 输出步长，SegFormer不使用此参数，但为了与DeepLabV3+接口兼容而保留
        pretrained_backbone (bool): 是否使用预训练的骨干网络
    
    Returns:
        SegFormerWrapper: 封装的SegFormer模型
    """
    return SegFormerWrapper(model_name="nvidia/mit-b0", num_classes=num_classes, pretrained_backbone=pretrained_backbone, output_stride=output_stride)

def segformer_b1(num_classes=21, output_stride=None, pretrained_backbone=True):
    """
    创建SegFormer-B1模型
    
    Args:
        num_classes (int): 分类类别数量
        output_stride (int): 输出步长，SegFormer不使用此参数，但为了与DeepLabV3+接口兼容而保留
        pretrained_backbone (bool): 是否使用预训练的骨干网络
    
    Returns:
        SegFormerWrapper: 封装的SegFormer模型
    """
    return SegFormerWrapper(model_name="nvidia/mit-b1", num_classes=num_classes, pretrained_backbone=pretrained_backbone, output_stride=output_stride)

def segformer_b2(num_classes=21, output_stride=None, pretrained_backbone=True):
    """
    创建SegFormer-B2模型
    
    Args:
        num_classes (int): 分类类别数量
        output_stride (int): 输出步长，SegFormer不使用此参数，但为了与DeepLabV3+接口兼容而保留
        pretrained_backbone (bool): 是否使用预训练的骨干网络
    
    Returns:
        SegFormerWrapper: 封装的SegFormer模型
    """
    return SegFormerWrapper(model_name="nvidia/mit-b2", num_classes=num_classes, pretrained_backbone=pretrained_backbone, output_stride=output_stride)

def segformer_b3(num_classes=21, output_stride=None, pretrained_backbone=True):
    """
    创建SegFormer-B3模型
    
    Args:
        num_classes (int): 分类类别数量
        output_stride (int): 输出步长，SegFormer不使用此参数，但为了与DeepLabV3+接口兼容而保留
        pretrained_backbone (bool): 是否使用预训练的骨干网络
    
    Returns:
        SegFormerWrapper: 封装的SegFormer模型
    """
    return SegFormerWrapper(model_name="nvidia/mit-b3", num_classes=num_classes, pretrained_backbone=pretrained_backbone, output_stride=output_stride)

def segformer_b4(num_classes=21, output_stride=None, pretrained_backbone=True):
    """
    创建SegFormer-B4模型
    
    Args:
        num_classes (int): 分类类别数量
        output_stride (int): 输出步长，SegFormer不使用此参数，但为了与DeepLabV3+接口兼容而保留
        pretrained_backbone (bool): 是否使用预训练的骨干网络
    
    Returns:
        SegFormerWrapper: 封装的SegFormer模型
    """
    return SegFormerWrapper(model_name="nvidia/mit-b4", num_classes=num_classes, pretrained_backbone=pretrained_backbone, output_stride=output_stride)

def segformer_b5(num_classes=21, output_stride=None, pretrained_backbone=True):
    """
    创建SegFormer-B5模型
    
    Args:
        num_classes (int): 分类类别数量
        output_stride (int): 输出步长，SegFormer不使用此参数，但为了与DeepLabV3+接口兼容而保留
        pretrained_backbone (bool): 是否使用预训练的骨干网络
    
    Returns:
        SegFormerWrapper: 封装的SegFormer模型
    """
    return SegFormerWrapper(model_name="nvidia/mit-b5", num_classes=num_classes, pretrained_backbone=pretrained_backbone, output_stride=output_stride)
