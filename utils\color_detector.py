#!/usr/bin/env python3
"""
Automatic Color Detection Tool for Semantic Segmentation Datasets

This tool automatically detects unique colors in semantic segmentation masks
and can generate color mappings for different datasets.

Usage:
    python utils/color_detector.py --dataset_path ./datasets/data/Jiayu
    python utils/color_detector.py --mask_path ./path/to/single/mask.png
    python utils/color_detector.py --json_path ./path/to/metadata.json
"""

import os
import json
import argparse
import numpy as np
from PIL import Image
from collections import Counter
import matplotlib.pyplot as plt


class ColorDetector:
    """Automatic color detection for semantic segmentation datasets"""
    
    def __init__(self):
        self.detected_colors = {}
        self.color_stats = {}
    
    def analyze_dataset(self, dataset_path, max_samples=10):
        """
        Analyze a complete dataset directory
        
        Args:
            dataset_path (str): Path to dataset directory
            max_samples (int): Maximum number of samples to analyze
            
        Returns:
            dict: Color mapping and statistics
        """
        print(f"Analyzing dataset: {dataset_path}")
        
        # Find mask and JSON files
        mask_files = []
        json_files = []
        
        for filename in os.listdir(dataset_path):
            if 'semantic segmentation' in filename and filename.endswith('.png'):
                mask_files.append(os.path.join(dataset_path, filename))
            elif filename.endswith('.json'):
                json_files.append(os.path.join(dataset_path, filename))
        
        print(f"Found {len(mask_files)} mask files and {len(json_files)} JSON files")
        
        # Limit samples for efficiency
        mask_files = mask_files[:max_samples]
        json_files = json_files[:max_samples]
        
        # Method 1: Extract from JSON metadata
        json_colors = self.extract_colors_from_json_files(json_files)
        
        # Method 2: Analyze mask images
        mask_colors = self.analyze_mask_files(mask_files)
        
        # Combine results
        results = {
            'json_colors': json_colors,
            'mask_colors': mask_colors,
            'recommended_mapping': self.generate_color_mapping(json_colors, mask_colors)
        }
        
        return results
    
    def extract_colors_from_json_files(self, json_files):
        """Extract color information from JSON metadata files"""
        print("Extracting colors from JSON metadata...")
        
        color_mapping = {}
        
        for json_file in json_files:
            try:
                with open(json_file, 'r') as f:
                    data = json.load(f)
                
                # Navigate to semantic segmentation annotations
                captures = data.get('captures', [])
                for capture in captures:
                    annotations = capture.get('annotations', [])
                    for annotation in annotations:
                        if 'SemanticSegmentation' in annotation.get('@type', ''):
                            instances = annotation.get('instances', [])
                            
                            for instance in instances:
                                label_name = instance.get('labelName', '')
                                pixel_value = instance.get('pixelValue', [])
                                
                                if label_name and len(pixel_value) >= 3:
                                    color_mapping[label_name] = pixel_value
                                    print(f"  {label_name}: {pixel_value}")
            
            except Exception as e:
                print(f"Error reading {json_file}: {e}")
        
        return color_mapping
    
    def analyze_mask_files(self, mask_files):
        """Analyze mask images to detect unique colors and their frequencies"""
        print("Analyzing mask images...")
        
        all_colors = Counter()
        color_pixels = Counter()
        
        for mask_file in mask_files:
            try:
                mask_img = Image.open(mask_file)
                mask_array = np.array(mask_img)
                
                # Handle different image formats
                if len(mask_array.shape) == 2:
                    # Grayscale - convert to RGB
                    mask_array = np.stack([mask_array] * 3, axis=-1)
                elif len(mask_array.shape) == 3 and mask_array.shape[2] == 3:
                    # RGB - add alpha channel
                    alpha = np.full((mask_array.shape[0], mask_array.shape[1], 1), 255, dtype=mask_array.dtype)
                    mask_array = np.concatenate([mask_array, alpha], axis=2)
                
                # Get unique colors and their counts
                if len(mask_array.shape) == 3:
                    pixels = mask_array.reshape(-1, mask_array.shape[2])
                    unique_colors, counts = np.unique(pixels, axis=0, return_counts=True)
                    
                    for color, count in zip(unique_colors, counts):
                        color_tuple = tuple(color)
                        all_colors[color_tuple] += 1  # Number of images containing this color
                        color_pixels[color_tuple] += count  # Total pixel count
            
            except Exception as e:
                print(f"Error analyzing {mask_file}: {e}")
        
        # Sort by frequency
        sorted_colors = sorted(all_colors.items(), key=lambda x: x[1], reverse=True)
        
        print(f"Found {len(sorted_colors)} unique colors:")
        for color, freq in sorted_colors:
            pixel_count = color_pixels[color]
            print(f"  {color}: appears in {freq} images, {pixel_count} total pixels")
        
        return {
            'unique_colors': sorted_colors,
            'pixel_counts': dict(color_pixels)
        }
    
    def generate_color_mapping(self, json_colors, mask_colors):
        """Generate recommended color mapping based on analysis"""
        print("\nGenerating recommended color mapping...")
        
        mapping = {}
        
        # If we have JSON colors, use them as ground truth
        if json_colors:
            class_id = 0
            for label_name, color in json_colors.items():
                mapping[class_id] = {
                    'name': label_name,
                    'color': color,
                    'source': 'json_metadata'
                }
                class_id += 1
        
        # If no JSON colors, infer from mask analysis
        elif mask_colors and 'unique_colors' in mask_colors:
            unique_colors = mask_colors['unique_colors']
            pixel_counts = mask_colors['pixel_counts']
            
            # Sort by pixel count (most frequent = background)
            sorted_by_pixels = sorted(pixel_counts.items(), key=lambda x: x[1], reverse=True)
            
            class_id = 0
            for color, pixel_count in sorted_by_pixels:
                # Infer class names based on color characteristics
                if class_id == 0:
                    class_name = 'Background'
                elif sum(color[:3]) < 100:  # Dark colors
                    class_name = f'Dark_Object_{class_id}'
                elif sum(color[:3]) > 600:  # Bright colors
                    class_name = f'Bright_Object_{class_id}'
                else:
                    class_name = f'Object_{class_id}'
                
                mapping[class_id] = {
                    'name': class_name,
                    'color': list(color),
                    'pixel_count': pixel_count,
                    'source': 'mask_analysis'
                }
                class_id += 1
        
        return mapping
    
    def visualize_colors(self, color_mapping, save_path=None):
        """Create a visualization of detected colors"""
        if not color_mapping:
            print("No colors to visualize")
            return
        
        fig, ax = plt.subplots(1, 1, figsize=(12, 6))
        
        colors = []
        labels = []
        
        for class_id, info in color_mapping.items():
            color = info['color']
            name = info['name']
            
            # Normalize color to 0-1 range for matplotlib
            if len(color) >= 3:
                rgb_color = [c/255.0 for c in color[:3]]
                colors.append(rgb_color)
                labels.append(f"Class {class_id}: {name}")
        
        # Create color patches
        y_pos = np.arange(len(colors))
        bars = ax.barh(y_pos, [1] * len(colors), color=colors)
        
        ax.set_yticks(y_pos)
        ax.set_yticklabels(labels)
        ax.set_xlabel('Color Visualization')
        ax.set_title('Detected Color Mapping')
        
        # Add color values as text
        for i, (bar, info) in enumerate(zip(bars, color_mapping.values())):
            color_text = str(info['color'])
            ax.text(0.5, bar.get_y() + bar.get_height()/2, color_text, 
                   ha='center', va='center', fontweight='bold')
        
        plt.tight_layout()
        
        if save_path:
            plt.savefig(save_path, dpi=150, bbox_inches='tight')
            print(f"Visualization saved to: {save_path}")
        
        plt.show()
    
    def generate_code(self, color_mapping, class_name="AutoDetectedDataset"):
        """Generate Python code for the detected color mapping"""
        if not color_mapping:
            return ""
        
        code = f"""
# Auto-generated color mapping for {class_name}
class {class_name}:
    def __init__(self):
        self.num_classes = {len(color_mapping)}
        self.class_names = {[info['name'] for info in color_mapping.values()]}
        
        # Color mapping (RGBA format)
        self.color_mapping = {{
"""
        
        for class_id, info in color_mapping.items():
            color = info['color']
            name = info['name']
            code += f"            {class_id}: {color},  # {name}\n"
        
        code += """        }
        
    def get_class_colors(self):
        \"\"\"Get class colors for visualization (RGB format)\"\"\"
        return [color[:3] for color in self.color_mapping.values()]
    
    def decode_target(self, target):
        \"\"\"Convert class indices to RGB colors\"\"\"
        color_map = np.array([color[:3] for color in self.color_mapping.values()], dtype=np.uint8)
        target = np.clip(target, 0, len(color_map) - 1)
        return color_map[target]
"""
        
        return code


def main():
    parser = argparse.ArgumentParser(description='Automatic Color Detection for Semantic Segmentation')
    parser.add_argument('--dataset_path', type=str, help='Path to dataset directory')
    parser.add_argument('--mask_path', type=str, help='Path to single mask file')
    parser.add_argument('--json_path', type=str, help='Path to single JSON metadata file')
    parser.add_argument('--max_samples', type=int, default=10, help='Maximum samples to analyze')
    parser.add_argument('--visualize', action='store_true', help='Show color visualization')
    parser.add_argument('--generate_code', action='store_true', help='Generate Python code')
    parser.add_argument('--output', type=str, help='Output file for generated code')
    
    args = parser.parse_args()
    
    detector = ColorDetector()
    
    if args.dataset_path:
        results = detector.analyze_dataset(args.dataset_path, args.max_samples)
        color_mapping = results['recommended_mapping']
        
        print("\n" + "="*50)
        print("ANALYSIS RESULTS")
        print("="*50)
        
        print("\nRecommended Color Mapping:")
        for class_id, info in color_mapping.items():
            print(f"  Class {class_id}: {info['name']} -> {info['color']}")
        
        if args.visualize:
            detector.visualize_colors(color_mapping)
        
        if args.generate_code:
            code = detector.generate_code(color_mapping)
            print("\nGenerated Code:")
            print(code)
            
            if args.output:
                with open(args.output, 'w') as f:
                    f.write(code)
                print(f"Code saved to: {args.output}")
    
    elif args.mask_path:
        results = detector.analyze_mask_files([args.mask_path])
        print("Mask analysis results:", results)
    
    elif args.json_path:
        results = detector.extract_colors_from_json_files([args.json_path])
        print("JSON analysis results:", results)
    
    else:
        print("Please provide --dataset_path, --mask_path, or --json_path")


if __name__ == '__main__':
    main()
