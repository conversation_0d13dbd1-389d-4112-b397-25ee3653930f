import os
import numpy as np
from PIL import Image
import torch
from torch.utils import data
from datasets import utils


class LunarSimDataset(data.Dataset):
    """LunarSim Dataset for semantic segmentation

    A lunar surface simulation dataset containing terrain segmentation data.
    Dataset contains 1641 samples across 4 sequences with RGB images and semantic segmentation masks.

    Classes:
        0: Background (lunar surface)
        1: Rock (rocks and other lunar features)
    """
    
    def __init__(self, root, split='train', transform=None):
        """
        Args:
            root (str): Root directory of the dataset
            split (str): Dataset split ('train', 'val', 'test')
            transform: Data augmentation transforms
        """
        self.root = os.path.expanduser(root)
        self.split = split
        self.transform = transform
        
        # Dataset path
        self.dataset_path = os.path.join(self.root, 'LunarSim')
        
        # Class information
        self.num_classes = 2  # Background + Rock objects
        self.class_names = ['Background', 'Rock']
        self.ignore_index = 255
        
        # Color mapping for visualization (RGB format)
        self.background_color = [0, 0, 0]      # Black for background
        self.rock_color = [0, 255, 54]         # Green for rock objects
        
        # Get all available samples
        self.samples = self._get_samples()
        
        print(f"Color Detection: Background={self.background_color}, Rock={self.rock_color}")
        
        # Split dataset: 70% train, 20% val, 10% test
        self.train_samples, self.val_samples, self.test_samples = self._split_dataset()
        
        # Select samples based on split
        if split == 'train':
            self.current_samples = self.train_samples
        elif split == 'val':
            self.current_samples = self.val_samples
        elif split == 'test':
            self.current_samples = self.test_samples
        else:
            raise ValueError(f"Invalid split: {split}. Must be 'train', 'val', or 'test'")
            
        print(f"LunarSim Dataset - {split}: {len(self.current_samples)} samples")

    def _get_samples(self):
        """Get all available samples from the dataset directory"""
        samples = []
        
        images_path = os.path.join(self.dataset_path, 'images')
        masks_path = os.path.join(self.dataset_path, 'maks')  # Note: keeping original directory name
        
        if not os.path.exists(images_path):
            raise FileNotFoundError(f"Images path not found: {images_path}")
        if not os.path.exists(masks_path):
            raise FileNotFoundError(f"Masks path not found: {masks_path}")
        
        # Find all image files
        for filename in os.listdir(images_path):
            if filename.endswith('.png') and '_rgb_' in filename:
                # Extract sequence and timestamp
                parts = filename.replace('.png', '').split('_')
                if len(parts) == 3:
                    seq, _, timestamp = parts
                    
                    # Check if corresponding mask exists
                    img_path = os.path.join(images_path, filename)
                    mask_filename = f"{seq}_segmentation_{timestamp}.png"
                    mask_path = os.path.join(masks_path, mask_filename)
                    
                    if os.path.exists(mask_path):
                        samples.append({
                            'sequence': seq,
                            'timestamp': timestamp,
                            'image': img_path,
                            'mask': mask_path
                        })
        
        # Sort by sequence and timestamp
        samples.sort(key=lambda x: (x['sequence'], int(x['timestamp'])))
        return samples
    
    def _split_dataset(self):
        """Split dataset into train/val/test with 7:2:1 ratio"""
        total_samples = len(self.samples)
        
        # Calculate split indices
        train_end = int(0.7 * total_samples)
        val_end = int(0.9 * total_samples)
        
        train_samples = self.samples[:train_end]
        val_samples = self.samples[train_end:val_end]
        test_samples = self.samples[val_end:]
        
        print(f"Dataset split - Train: {len(train_samples)}, Val: {len(val_samples)}, Test: {len(test_samples)}")
        
        return train_samples, val_samples, test_samples
    
    def _convert_mask(self, mask_pil):
        """Convert semantic segmentation mask to class indices
        
        Args:
            mask_pil: PIL Image of the semantic segmentation mask (RGBA format)

        Returns:
            numpy array with class indices (0: Background, 1: Rock)
        """
        mask_array = np.array(mask_pil)
        
        # Initialize output mask with background class (0)
        output_mask = np.zeros((mask_array.shape[0], mask_array.shape[1]), dtype=np.uint8)
        
        # Handle RGBA format - segmentation info is in G channel
        if len(mask_array.shape) == 3 and mask_array.shape[2] >= 2:
            # Extract G channel (green channel contains the segmentation information)
            g_channel = mask_array[:, :, 1]
            
            # Rock pixels are marked with 255 in G channel
            rock_pixels = (g_channel == 255)
            output_mask[rock_pixels] = 1  # Rock class
            
            # Background pixels remain 0 (already initialized)
        
        return output_mask
    
    def __getitem__(self, index):
        """Get a sample from the dataset"""
        sample = self.current_samples[index]
        
        # Load image and convert to RGB
        image = Image.open(sample['image']).convert('RGB')
        
        # Load and convert mask
        mask_pil = Image.open(sample['mask'])
        mask = self._convert_mask(mask_pil)
        mask = Image.fromarray(mask)
        
        # Apply transforms
        if self.transform is not None:
            image, mask = self.transform(image, mask)
        
        return image, mask
    
    def __len__(self):
        return len(self.current_samples)
    
    def get_class_names(self):
        """Get class names"""
        return self.class_names
    
    def get_class_colors(self):
        """Get class colors for visualization (RGB format)"""
        return [
            self.background_color,  # Background
            self.rock_color,        # Rock objects
        ]

    def decode_target(self, target):
        """Convert class indices to RGB colors for visualization

        Args:
            target: numpy array with class indices (0: Background, 1: Rock)

        Returns:
            numpy array with RGB colors for visualization
        """
        # Define color mapping
        color_map = np.array([
            self.background_color,  # Background
            self.rock_color,        # Rock objects
        ], dtype=np.uint8)
        
        # Handle invalid class indices
        target = np.clip(target, 0, len(color_map) - 1)
        
        # Convert to RGB
        rgb_target = color_map[target]
        
        return rgb_target


if __name__ == '__main__':
    # Test the dataset
    import matplotlib.pyplot as plt
    
    # Test dataset loading
    dataset_root = '../datasets/data'
    
    for split in ['train', 'val', 'test']:
        try:
            dataset = LunarSimDataset(root=dataset_root, split=split)
            print(f"\n{split.upper()} Dataset:")
            print(f"  Samples: {len(dataset)}")
            print(f"  Classes: {dataset.num_classes}")
            print(f"  Class names: {dataset.get_class_names()}")
            
            if len(dataset) > 0:
                # Test loading first sample
                image, mask = dataset[0]
                print(f"  Sample 0 - Image shape: {np.array(image).shape}, Mask shape: {np.array(mask).shape}")
                print(f"  Mask unique values: {np.unique(np.array(mask))}")
                
        except Exception as e:
            print(f"Error loading {split} dataset: {e}")
