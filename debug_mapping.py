import os
import numpy as np
from PIL import Image

def analyze_original_masks():
    """分析原始掩码文件的像素值分布"""
    dataset_path = 'datasets/data/RealLunar'
    mask_files = ['g_PCAM1.png', 'g_PCAM2.png', 'g_TCAM1.png', 'g_TCAM2.png']
    
    print("=== 原始掩码文件像素值分布 ===")
    
    all_pixel_counts = {}
    total_files = 0
    
    for mask_file in mask_files:
        mask_path = os.path.join(dataset_path, mask_file)
        if os.path.exists(mask_path):
            total_files += 1
            mask_img = Image.open(mask_path).convert('L')
            mask_array = np.array(mask_img, dtype=np.uint8)
            
            unique_values, counts = np.unique(mask_array, return_counts=True)
            total_pixels = mask_array.size
            
            print(f'\n{mask_file} (总像素: {total_pixels}):')
            for val, count in zip(unique_values, counts):
                percentage = (count / total_pixels) * 100
                print(f'  像素值 {val}: {count} 像素 ({percentage:.2f}%)')
                
                # 累计统计
                if val not in all_pixel_counts:
                    all_pixel_counts[val] = 0
                all_pixel_counts[val] += count
    
    print(f'\n=== 所有{total_files}个文件的总体分布 ===')
    total_all_pixels = sum(all_pixel_counts.values())
    for val in sorted(all_pixel_counts.keys()):
        count = all_pixel_counts[val]
        percentage = (count / total_all_pixels) * 100
        print(f'  像素值 {val}: {count} 像素 ({percentage:.2f}%)')
    
    return all_pixel_counts

def test_current_mapping():
    """测试当前的映射逻辑"""
    print(f'\n=== 当前映射逻辑 ===')
    print(f'  像素值 0 -> 类别 0 (Background)')
    print(f'  像素值 76 -> 类别 1 (Small_Rocks)')
    print(f'  像素值 150 -> 类别 2 (Large_Rocks)')
    print(f'  像素值 29 -> 类别 3 (Sky)')
    
    # 模拟映射过程
    test_pixels = np.array([0, 29, 76, 150, 255, 128])  # 包含一些其他值
    class_mask = np.zeros_like(test_pixels)
    
    class_mask[test_pixels == 0] = 0
    class_mask[test_pixels == 76] = 1
    class_mask[test_pixels == 150] = 2
    class_mask[test_pixels == 29] = 3
    
    # 处理其他值
    other_values = ~np.isin(test_pixels, [0, 29, 76, 150])
    if np.any(other_values):
        reference_values = [0, 76, 150, 29]  # 对应类别 [0, 1, 2, 3]
        reference_classes = [0, 1, 2, 3]
        
        for pixel_val in test_pixels[other_values]:
            distances = [abs(pixel_val - v) for v in reference_values]
            closest_idx = distances.index(min(distances))
            closest_class = reference_classes[closest_idx]
            class_mask[test_pixels == pixel_val] = closest_class
    
    print(f'\n测试映射结果:')
    for orig, mapped in zip(test_pixels, class_mask):
        print(f'  像素值 {orig} -> 类别 {mapped}')

def analyze_dataset_output():
    """分析数据集输出的类别分布"""
    print(f'\n=== 数据集输出分析 ===')
    
    import sys
    sys.path.append('.')
    
    try:
        from datasets.reallunar import RealLunarDataset
        
        dataset = RealLunarDataset(root='datasets/data', split='test', merge_rocks=False)
        print(f'数据集加载成功，共 {len(dataset)} 个样本')
        
        if len(dataset) > 0:
            # 分析前几个样本
            for i in range(min(3, len(dataset))):
                image, mask = dataset[i]
                mask_array = np.array(mask)
                unique_classes, counts = np.unique(mask_array, return_counts=True)
                total_pixels = mask_array.size
                
                print(f'\n样本 {i} (总像素: {total_pixels}):')
                for cls, count in zip(unique_classes, counts):
                    percentage = (count / total_pixels) * 100
                    class_name = dataset.class_names[cls] if cls < len(dataset.class_names) else f"Unknown_{cls}"
                    print(f'  类别 {cls} ({class_name}): {count} 像素 ({percentage:.2f}%)')
                    
    except Exception as e:
        print(f'数据集分析出错: {e}')
        import traceback
        traceback.print_exc()

if __name__ == '__main__':
    # 分析原始掩码
    pixel_counts = analyze_original_masks()
    
    # 测试当前映射
    test_current_mapping()
    
    # 分析数据集输出
    analyze_dataset_output()
    
    print(f'\n=== 问题诊断 ===')
    print(f'如果类别3的准确率是100%，可能的原因：')
    print(f'1. 像素值29在数据中很少出现')
    print(f'2. 模型预测该类别的像素数为0，而真实标签中该类别也为0')
    print(f'3. 映射逻辑有问题')
    
    if 29 in pixel_counts:
        total_pixels = sum(pixel_counts.values())
        percentage_29 = (pixel_counts[29] / total_pixels) * 100
        print(f'像素值29占总像素的 {percentage_29:.4f}%')
        if percentage_29 < 0.1:
            print(f'⚠️  像素值29非常稀少，这可能解释了100%准确率')
    else:
        print(f'⚠️  像素值29在检查的文件中不存在！')
