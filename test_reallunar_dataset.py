#!/usr/bin/env python3
"""
Test the modified RealLunar dataset
"""

import sys
import os
sys.path.append('.')

from datasets.reallunar import RealLunarDataset
import numpy as np
import matplotlib.pyplot as plt

def test_dataset():
    dataset_root = 'datasets/data'
    
    print("=== Testing RealLunar Dataset ===")
    
    # Test both modes
    for merge_rocks in [False, True]:
        mode_name = "3-class (merged)" if merge_rocks else "4-class (full)"
        print(f"\n--- Testing {mode_name} mode ---")
        
        try:
            # Test with 'test' split to get all data
            dataset = RealLunarDataset(root=dataset_root, split='test', merge_rocks=merge_rocks)
            print(f"Dataset loaded successfully!")
            print(f"  Samples: {len(dataset)}")
            print(f"  Classes: {dataset.num_classes}")
            print(f"  Class names: {dataset.get_class_names()}")
            
            if len(dataset) > 0:
                # Test loading first sample
                image, mask = dataset[0]
                print(f"  Sample 0:")
                print(f"    Image shape: {np.array(image).shape}")
                print(f"    Mask shape: {np.array(mask).shape}")
                print(f"    Mask unique values: {np.unique(np.array(mask))}")
                print(f"    Mask dtype: {np.array(mask).dtype}")
                
                # Count pixels for each class
                mask_array = np.array(mask)
                unique_values, counts = np.unique(mask_array, return_counts=True)
                total_pixels = mask_array.size
                
                print(f"    Class distribution:")
                for val, count in zip(unique_values, counts):
                    percentage = (count / total_pixels) * 100
                    class_name = dataset.class_names[val] if val < len(dataset.class_names) else f"Unknown_{val}"
                    print(f"      Class {val} ({class_name}): {count} pixels ({percentage:.2f}%)")
                
        except Exception as e:
            print(f"Error loading dataset: {e}")
            import traceback
            traceback.print_exc()

if __name__ == '__main__':
    test_dataset()
