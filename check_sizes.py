import os
from PIL import Image

dataset_path = 'datasets/data/RealLunar'

# 检查几个图像和掩码对的尺寸
pairs_to_check = [
    ('PCAM1.png', 'g_PCAM1.png'),
    ('PCAM2.png', 'g_PCAM2.png'),
    ('TCAM1.png', 'g_TCAM1.png'),
    ('TCAM2.png', 'g_TCAM2.png')
]

print("Checking image and mask size compatibility:")
print("=" * 50)

for img_name, mask_name in pairs_to_check:
    img_path = os.path.join(dataset_path, img_name)
    mask_path = os.path.join(dataset_path, mask_name)
    
    if os.path.exists(img_path) and os.path.exists(mask_path):
        img = Image.open(img_path)
        mask = Image.open(mask_path)
        
        print(f'{img_name}: {img.size} vs {mask_name}: {mask.size}')
        if img.size != mask.size:
            print(f'  ❌ SIZE MISMATCH!')
        else:
            print(f'  ✅ Sizes match')
    else:
        print(f'Missing files: {img_name} or {mask_name}')

print("\n" + "=" * 50)
print("Checking all available pairs...")

# 检查所有可用的图像文件
image_files = [f for f in os.listdir(dataset_path) if f.endswith('.png') and not f.startswith('g_')]
mask_files = [f for f in os.listdir(dataset_path) if f.startswith('g_') and f.endswith('.png')]

print(f"Found {len(image_files)} image files and {len(mask_files)} mask files")

mismatched_pairs = []

for img_file in image_files[:10]:  # Check first 10 pairs
    # Find corresponding mask file
    if img_file.startswith('PCAM'):
        mask_file = f"g_{img_file}"
    elif img_file.startswith('TCAM'):
        mask_file = f"g_{img_file}"
    else:
        continue
    
    img_path = os.path.join(dataset_path, img_file)
    mask_path = os.path.join(dataset_path, mask_file)
    
    if os.path.exists(mask_path):
        img = Image.open(img_path)
        mask = Image.open(mask_path)
        
        if img.size != mask.size:
            mismatched_pairs.append((img_file, mask_file, img.size, mask.size))
            print(f"MISMATCH: {img_file} {img.size} vs {mask_file} {mask.size}")

if mismatched_pairs:
    print(f"\nFound {len(mismatched_pairs)} mismatched pairs!")
    print("This needs to be fixed before training.")
else:
    print("\nAll checked pairs have matching sizes! ✅")
