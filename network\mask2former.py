import torch
import torch.nn as nn
import torch.nn.functional as F
from transformers import Mask2FormerForUniversalSegmentation, Mask2FormerImageProcessor

class Mask2FormerWrapper(nn.Module):
    """
    Mask2Former模型的封装类，用于集成到DeepLabV3Plus-Pytorch项目中
    
    Mask2Former是一种通用的分割模型，可以处理语义分割、实例分割和全景分割任务。
    该封装类使其能够与DeepLabV3Plus项目的接口兼容。
    """
    def __init__(self, model_name="facebook/mask2former-swin-large-cityscapes-semantic", 
                 num_classes=21, pretrained_backbone=True, output_stride=None):
        """
        初始化Mask2Former模型
        
        Args:
            model_name (str): 模型名称，如"facebook/mask2former-swin-large-cityscapes-semantic"
            num_classes (int): 分类类别数量
            pretrained_backbone (bool): 是否使用预训练的模型
            output_stride (int): 输出步长，Mask2Former不使用此参数，但为了与DeepLabV3+接口兼容而保留
        """
        super(Mask2FormerWrapper, self).__init__()
        
        self.model_name = model_name
        self.num_classes = num_classes
        
        # 添加图像处理器，用于模型输入预处理和输出后处理
        self.image_processor = Mask2FormerImageProcessor.from_pretrained(self.model_name)
        
        # 获取图像处理器的归一化参数
        self.mean = torch.tensor(self.image_processor.image_mean).view(1, 3, 1, 1)
        self.std = torch.tensor(self.image_processor.image_std).view(1, 3, 1, 1)
        
        if pretrained_backbone:
            # 使用预训练模型，并根据类别数量调整输出层
            self.model = Mask2FormerForUniversalSegmentation.from_pretrained(
                self.model_name,
                num_labels=num_classes,
                ignore_mismatched_sizes=True
            )
        else:
            # 从头开始训练，创建配置并初始化模型
            self.model = Mask2FormerForUniversalSegmentation.from_pretrained(
                self.model_name,
                num_labels=num_classes,
                ignore_mismatched_sizes=True
            )
        
        # 为了与DeepLabV3+接口兼容，添加backbone属性
        # 这个属性在main.py中被用于设置BN层的momentum
        self.backbone = nn.Sequential()  # 创建一个空的Sequential模块作为占位符
        
        # 记录是否为语义分割任务
        self.is_semantic = 'semantic' in model_name
        
        # 记录设备信息，用于归一化参数的设备转换
        self.device = None
    
    def forward(self, x):
        """
        前向传播函数
        
        Args:
            x (torch.Tensor): 输入图像张量，形状为[B, C, H, W]
        
        Returns:
            torch.Tensor: 分割结果，形状为[B, num_classes, H, W]
        """
        # 记录输入尺寸，用于后续上采样
        input_shape = x.shape[-2:]
        batch_size = x.shape[0]
        
        # 记录设备信息
        if self.device is None:
            self.device = x.device
            self.mean = self.mean.to(self.device)
            self.std = self.std.to(self.device)
        
        # 检查输入是否需要归一化处理
        # Mask2Former预期输入范围为[0,1]，并且需要特定的归一化参数
        if x.max() > 1.0:
            # 将像素值从[0,255]缩放到[0,1]
            x = x / 255.0
        
        # 应用Mask2Former预期的归一化
        x_normalized = (x - self.mean) / self.std

        # 处理batch_size=1的情况，避免BatchNorm错误
        if batch_size == 1 and self.training:
            # 临时切换到eval模式进行前向传播，但保持梯度计算
            was_training = self.model.training
            self.model.eval()

            # 不使用no_grad，保持梯度计算
            outputs = self.model(pixel_values=x_normalized)

            # 恢复训练模式
            if was_training:
                self.model.train()
        else:
            # 正常前向传播
            outputs = self.model(pixel_values=x_normalized)
        
        if self.is_semantic:
            # 语义分割任务的处理方式
            # 获取类别查询logits和掩码查询logits
            class_logits = outputs.class_queries_logits  # [B, num_queries, num_classes+1]
            mask_logits = outputs.masks_queries_logits   # [B, num_queries, H', W']
            
            # 尝试使用模型的内部方法进行处理
            try:
                # 如果模型有语义分割头，直接使用它
                if hasattr(self.model, 'semantic_segmentation_head'):
                    logits = self.model.semantic_segmentation_head(
                        mask_logits, class_logits
                    )
                    
                    # 使用双线性插值将分割图上采样到输入图像的尺寸
                    upsampled_logits = F.interpolate(
                        logits,
                        size=input_shape,
                        mode="bilinear",
                        align_corners=False
                    )
                    
                    return upsampled_logits
                
                # 如果没有语义分割头，使用自定义处理逻辑
                else:
                    # 移除类别logits中的null类（最后一个类别）
                    class_logits = class_logits[:, :, :-1]  # [B, num_queries, num_classes]
                    
                    # 对类别logits进行softmax，得到每个查询的类别概率
                    class_probs = torch.softmax(class_logits, dim=-1)  # [B, num_queries, num_classes]
                    
                    # 将类别概率与掩码logits相乘，得到每个类别的掩码
                    # 首先调整class_probs的形状以便进行广播
                    # [B, num_queries, num_classes] -> [B, num_queries, num_classes, 1, 1]
                    class_probs = class_probs.unsqueeze(-1).unsqueeze(-1)
                    
                    # 调整mask_logits的形状以便进行广播
                    # [B, num_queries, H', W'] -> [B, num_queries, 1, H', W']
                    mask_logits = mask_logits.unsqueeze(2)
                    
                    # 应用sigmoid激活函数到mask_logits
                    mask_probs = torch.sigmoid(mask_logits)  # [B, num_queries, 1, H', W']
                    
                    # 将类别概率与掩码概率相乘
                    # [B, num_queries, num_classes, 1, 1] * [B, num_queries, 1, H', W'] -> [B, num_queries, num_classes, H', W']
                    segmentation = class_probs * mask_probs
                    
                    # 在查询维度上求和，得到每个类别的最终分割图
                    # [B, num_queries, num_classes, H', W'] -> [B, num_classes, H', W']
                    segmentation = segmentation.sum(1)
                    
                    # 使用双线性插值将分割图上采样到输入图像的尺寸
                    upsampled_segmentation = F.interpolate(
                        segmentation,
                        size=input_shape,
                        mode="bilinear",
                        align_corners=False
                    )
                    
                    return upsampled_segmentation
            
            except Exception as e:
                # 如果出现异常，回退到原始处理逻辑
                print(f"警告：使用模型内部方法处理输出时出错：{e}，回退到自定义处理逻辑")
                
                # 移除类别logits中的null类（最后一个类别）
                class_logits = class_logits[:, :, :-1]  # [B, num_queries, num_classes]
                
                # 对类别logits进行softmax，得到每个查询的类别概率
                class_probs = torch.softmax(class_logits, dim=-1)  # [B, num_queries, num_classes]
                
                # 将类别概率与掩码logits相乘，得到每个类别的掩码
                class_probs = class_probs.unsqueeze(-1).unsqueeze(-1)
                mask_logits = mask_logits.unsqueeze(2)
                mask_probs = torch.sigmoid(mask_logits)
                segmentation = class_probs * mask_probs
                segmentation = segmentation.sum(1)
                
                # 使用双线性插值将分割图上采样到输入图像的尺寸
                upsampled_segmentation = F.interpolate(
                    segmentation,
                    size=input_shape,
                    mode="bilinear",
                    align_corners=False
                )
                
                return upsampled_segmentation
        else:
            # 实例分割或全景分割任务的处理方式
            # 使用官方图像处理器进行后处理
            try:
                processed_outputs = self.image_processor.post_process_semantic_segmentation(
                    outputs,
                    target_sizes=[(input_shape[0], input_shape[1])] * batch_size
                )
                
                # 将处理后的输出转换为与DeepLabV3+兼容的格式
                # 创建一个空的张量来存储结果
                result = torch.zeros((batch_size, self.num_classes, input_shape[0], input_shape[1]), 
                                    device=x.device)
                
                # 填充结果张量
                for i, output in enumerate(processed_outputs):
                    # 对于每个批次样本，将处理后的分割图转换为one-hot编码
                    segmentation = output
                    for class_idx in range(self.num_classes):
                        result[i, class_idx] = (segmentation == class_idx).float()
                
                return result
            except Exception as e:
                # 如果出现异常，回退到原始处理逻辑
                print(f"警告：使用图像处理器处理输出时出错：{e}，回退到自定义处理逻辑")
                
                # 回退到语义分割的处理逻辑
                class_logits = outputs.class_queries_logits
                mask_logits = outputs.masks_queries_logits
                
                # 移除类别logits中的null类（最后一个类别）
                class_logits = class_logits[:, :, :-1]  # [B, num_queries, num_classes]
                
                # 对类别logits进行softmax，得到每个查询的类别概率
                class_probs = torch.softmax(class_logits, dim=-1)  # [B, num_queries, num_classes]
                
                # 将类别概率与掩码logits相乘，得到每个类别的掩码
                class_probs = class_probs.unsqueeze(-1).unsqueeze(-1)
                mask_logits = mask_logits.unsqueeze(2)
                mask_probs = torch.sigmoid(mask_logits)
                segmentation = class_probs * mask_probs
                segmentation = segmentation.sum(1)
                
                # 使用双线性插值将分割图上采样到输入图像的尺寸
                upsampled_segmentation = F.interpolate(
                    segmentation,
                    size=input_shape,
                    mode="bilinear",
                    align_corners=False
                )
                
                return upsampled_segmentation

def mask2former_swin_tiny(num_classes=21, output_stride=None, pretrained_backbone=True):
    """
    创建基于Swin-Tiny骨干网络的Mask2Former模型
    
    Args:
        num_classes (int): 分类类别数量
        output_stride (int): 输出步长，Mask2Former不使用此参数，但为了与DeepLabV3+接口兼容而保留
        pretrained_backbone (bool): 是否使用预训练的骨干网络
    
    Returns:
        Mask2FormerWrapper: 封装的Mask2Former模型
    """
    return Mask2FormerWrapper(
        model_name="facebook/mask2former-swin-tiny-cityscapes-semantic", 
        num_classes=num_classes, 
        pretrained_backbone=pretrained_backbone, 
        output_stride=output_stride
    )

def mask2former_swin_large_cityscapes(num_classes=21, output_stride=None, pretrained_backbone=True):
    """
    创建基于Swin-Large骨干网络的Mask2Former模型（Cityscapes预训练）
    
    Args:
        num_classes (int): 分类类别数量
        output_stride (int): 输出步长，Mask2Former不使用此参数，但为了与DeepLabV3+接口兼容而保留
        pretrained_backbone (bool): 是否使用预训练的骨干网络
    
    Returns:
        Mask2FormerWrapper: 封装的Mask2Former模型
    """
    return Mask2FormerWrapper(
        model_name="facebook/mask2former-swin-large-cityscapes-semantic", 
        num_classes=num_classes, 
        pretrained_backbone=pretrained_backbone, 
        output_stride=output_stride
    )

def mask2former_swin_large_ade(num_classes=21, output_stride=None, pretrained_backbone=True):
    """
    创建基于Swin-Large骨干网络的Mask2Former模型（ADE20K预训练）
    
    Args:
        num_classes (int): 分类类别数量
        output_stride (int): 输出步长，Mask2Former不使用此参数，但为了与DeepLabV3+接口兼容而保留
        pretrained_backbone (bool): 是否使用预训练的骨干网络
    
    Returns:
        Mask2FormerWrapper: 封装的Mask2Former模型
    """
    return Mask2FormerWrapper(
        model_name="facebook/mask2former-swin-large-ade-semantic", 
        num_classes=num_classes, 
        pretrained_backbone=pretrained_backbone, 
        output_stride=output_stride
    )

def mask2former_swin_base_coco(num_classes=21, output_stride=None, pretrained_backbone=True):
    """
    创建基于Swin-Base骨干网络的Mask2Former模型（COCO预训练）
    
    Args:
        num_classes (int): 分类类别数量
        output_stride (int): 输出步长，Mask2Former不使用此参数，但为了与DeepLabV3+接口兼容而保留
        pretrained_backbone (bool): 是否使用预训练的骨干网络
    
    Returns:
        Mask2FormerWrapper: 封装的Mask2Former模型
    """
    return Mask2FormerWrapper(
        model_name="facebook/mask2former-swin-base-coco-instance", 
        num_classes=num_classes, 
        pretrained_backbone=pretrained_backbone, 
        output_stride=output_stride
    )

def mask2former_swin_base_ade(num_classes=21, output_stride=None, pretrained_backbone=True):
    """
    创建基于Swin-Base骨干网络的Mask2Former模型（ADE20K预训练）
    
    Args:
        num_classes (int): 分类类别数量
        output_stride (int): 输出步长，Mask2Former不使用此参数，但为了与DeepLabV3+接口兼容而保留
        pretrained_backbone (bool): 是否使用预训练的骨干网络
    
    Returns:
        Mask2FormerWrapper: 封装的Mask2Former模型
    """
    return Mask2FormerWrapper(
        model_name="facebook/mask2former-swin-base-ade-semantic", 
        num_classes=num_classes, 
        pretrained_backbone=pretrained_backbone, 
        output_stride=output_stride
    )

def mask2former_swin_tiny_ade(num_classes=21, output_stride=None, pretrained_backbone=True):
    """
    创建基于Swin-Tiny骨干网络的Mask2Former模型（ADE20K预训练）
    
    Args:
        num_classes (int): 分类类别数量
        output_stride (int): 输出步长，Mask2Former不使用此参数，但为了与DeepLabV3+接口兼容而保留
        pretrained_backbone (bool): 是否使用预训练的骨干网络
    
    Returns:
        Mask2FormerWrapper: 封装的Mask2Former模型
    """
    return Mask2FormerWrapper(
        model_name="facebook/mask2former-swin-tiny-ade-semantic", 
        num_classes=num_classes, 
        pretrained_backbone=pretrained_backbone, 
        output_stride=output_stride
    )

def mask2former_swin_small_ade(num_classes=21, output_stride=None, pretrained_backbone=True):
    """
    创建基于Swin-Small骨干网络的Mask2Former模型（ADE20K预训练）
    
    Args:
        num_classes (int): 分类类别数量
        output_stride (int): 输出步长，Mask2Former不使用此参数，但为了与DeepLabV3+接口兼容而保留
        pretrained_backbone (bool): 是否使用预训练的骨干网络
    
    Returns:
        Mask2FormerWrapper: 封装的Mask2Former模型
    """
    return Mask2FormerWrapper(
        model_name="facebook/mask2former-swin-small-ade-semantic", 
        num_classes=num_classes, 
        pretrained_backbone=pretrained_backbone, 
        output_stride=output_stride
    )
