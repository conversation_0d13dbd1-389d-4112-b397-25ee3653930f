import json
import os
from collections import namedtuple

import torch
import torch.utils.data as data
from PIL import Image
import numpy as np


class S5Mars(data.Dataset):
    """LabelMars Dataset.
    
    **Parameters:**
        - **root** (string): Root directory of dataset where directory 'leftImg8bit' and 'gtFine' or 'gtCoarse' are located.
        - **split** (string, optional): The image split to use, 'train', 'test' or 'val' if mode="gtFine" otherwise 'train', 'train_extra' or 'val'
        - **mode** (string, optional): The quality mode to use, 'gtFine' or 'gtCoarse' or 'color'. Can also be a list to output a tuple with all specified target types.
        - **transform** (callable, optional): A function/transform that takes in a PIL image and returns a transformed version. E.g, ``transforms.RandomCrop``
        - **target_transform** (callable, optional): A function/transform that takes in the target and transforms it.
    """

    # Based on https://github.com/mcordts/cityscapesScripts
    S5MarsClass = namedtuple('S5MarsClass', ['name', 'id', 'train_id', 'category', 'category_id',
                                                     'has_instances', 'ignore_in_eval', 'color'])
    classes = [
        S5MarsClass('sky',            0, 0, 'sky', 0, False, True, (0x56, 0x94, 0x1E)),# (0x56, 0x94, 0x1E)
        S5MarsClass('ridge',          1, 1, 'ridge', 0, False, True, (0xA0, 0x20, 0xF0)),
        S5MarsClass('soil',             2, 2, 'soil', 2, False, True, (0xCC, 0xFF, 0x42)),
        S5MarsClass('sand',           3, 3, 'sand', 0, False, True, (0xFF, 0x00, 0x00)),
        S5MarsClass('bedrock',               4, 4, 'bedrock', 0, False, False, (0x42, 0xCC, 0xFF)),
        S5MarsClass('rock',               5, 5, 'rock', 0, False, False, (0x42, 0xCC, 0xFF)),
        S5MarsClass('rover',               6, 6, 'rover', 0, False, False, (0x42, 0xCC, 0xFF)),
        S5MarsClass('trace',               7, 7, 'trace', 0, False, False, (0x42, 0xCC, 0xFF)),
        S5MarsClass('hole',               8, 8, 'hole', 0, False, False, (0x42, 0xCC, 0xFF)),
        S5MarsClass('NULL',               9, 9, 'NULL', 0, False, False, (0x42, 0xCC, 0xFF)),
    ]

    train_id_to_color = [c.color for c in classes if (c.train_id != -1 and c.train_id != 255)]
    train_id_to_color.append([0, 0, 0])
    train_id_to_color = np.array(train_id_to_color)
    id_to_train_id = np.array([c.train_id for c in classes])
    
    #train_id_to_color = [(0, 0, 0), (128, 64, 128), (70, 70, 70), (153, 153, 153), (107, 142, 35),
    #                      (70, 130, 180), (220, 20, 60), (0, 0, 142)]
    #train_id_to_color = np.array(train_id_to_color)
    #id_to_train_id = np.array([c.category_id for c in classes], dtype='uint8') - 1

    def __init__(self, root, split='train', mode='--', target_type='semantic', transform=None):
        self.root = os.path.expanduser(root)
        self.mode = '--'
        self.target_type = target_type
        self.images_dir = os.path.join(self.root, 'S5Mars/images', split)

        self.targets_dir = os.path.join(self.root, 'S5Mars/annotations', split)
        self.transform = transform

        self.split = split
        self.images = []
        self.targets = []

        if split not in ['train', 'test', 'val']:
            raise ValueError('Invalid split for mode! Please use split="train", split="test"'
                             ' or split="val"')
        
        if not os.path.isdir(self.images_dir) or not os.path.isdir(self.targets_dir):
            raise RuntimeError('Dataset not found or incomplete. Please make sure all required folders for the'
                              ' specified "split" and "mode" are inside the "root" directory')
        
        for file_name in os.listdir(self.images_dir):
            self.images.append(os.path.join(self.images_dir, file_name))
            
            targetname, file_extension = os.path.splitext(file_name)
            targetname= targetname + ".png"
            self.targets.append(os.path.join(self.targets_dir, targetname))

                
    @classmethod
    def encode_target(cls, target):
        return cls.id_to_train_id[np.array(target)]

    @classmethod
    def decode_target(cls, target):
        target[target == 255] = 9
        #target = target.astype('uint8') + 1
        return cls.train_id_to_color[target]

    def __getitem__(self, index):
        """
        Args:
            index (int): Index
        Returns:
            tuple: (image, target) where target is a tuple of all target types if target_type is a list with more
            than one item. Otherwise target is a json object if target_type="polygon", else the image segmentation.
        """
        image = Image.open(self.images[index]).convert('RGB')
        target = Image.open(self.targets[index])
        if self.transform:
            image, target = self.transform(image, target)
        target = self.encode_target(target)
        return image, target

    def __len__(self):
        return len(self.images)

    def _load_json(self, path):
        with open(path, 'r') as file:
            data = json.load(file)
        return data

    def _get_target_suffix(self, mode, target_type):
        if target_type == 'semantic':
            return '{}_instanceIds.png'.format(mode)